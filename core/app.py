from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from langchain_openai import OpenAIEmbeddings
import logging
import sys

# 确保日志系统初始化（仅在未配置时）
if not logging.getLogger().hasHandlers():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

# 导入自定义模块
from db.memory_store import AgentMemoryStore
from api.router import router, init_router
from config.settings import connection_args, openai_config, service_config
from utils.feishu_alarm import send_error_alarm, send_info_alarm


def create_app() -> FastAPI:
    """创建并配置主应用"""
    # 初始化 FastAPI 应用
    app = FastAPI(
        title="记忆向量存储API",
        description="基于OceanBase的向量存储服务，提供对Agent记忆的增删改查功能",
        version="1.0.0",
        openapi_url="/memory/openapi.json"
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 允许所有来源
        allow_credentials=True,
        allow_methods=["*"],  # 允许所有方法
        allow_headers=["*"],  # 允许所有头
    )

    # 初始化embedding模型
    memory_store = None
    try:
        # 记录环境信息
        import os
        env = os.environ.get("ENV", "dev")

        logging.info(f"当前环境: {env}")

        # 检查配置是否完整
        if not connection_args:
            raise ValueError("数据库配置为空，请检查环境配置")
        if not openai_config:
            raise ValueError("OpenAI配置为空，请检查环境配置")

        logging.info(
            f"服务配置: 主服务端口={service_config.get('port', 8000)}, 健康检查端口={service_config.get('health_port', 9195)}")
        logging.info(
            f"数据库配置: 主机={connection_args.get('host', 'N/A')}, 端口={connection_args.get('port', 'N/A')}, 数据库={connection_args.get('db_name', 'N/A')}")

        # 初始化OpenAI Embeddings模型
        logging.info("初始化OpenAI Embeddings模型")
        logging.info(
            f"OpenAI API配置: model={openai_config.get('model', 'N/A')}, base={openai_config.get('openai_api_base', 'N/A')}")

        embeddings = OpenAIEmbeddings(
            model=openai_config.get("model", "text-embedding-ada-002"),
            openai_api_key=openai_config.get("openai_api_key", ""),
            openai_api_base=openai_config.get("openai_api_base", ""),
            # 跳过上下文长度检查，直接发送文本
            check_embedding_ctx_length=False
        )
        logging.info("OpenAI Embeddings模型初始化成功")

        # 初始化记忆存储
        logging.info(f"尝试连接到数据库: {connection_args.get('host', 'N/A')}:{connection_args.get('port', 'N/A')}")
        memory_store = AgentMemoryStore(connection_args, embeddings)
        logging.info("记忆存储初始化成功")
    except Exception as e:
        logging.error(f"初始化失败: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())

        # 发送服务初始化失败告警
        send_error_alarm(f"服务初始化失败: {str(e)}", "服务初始化失败", traceback.format_exc())

        # 这样健康检查服务仍然可以启动，并报告错误状态
        memory_store = None

    # 初始化路由器
    init_router(memory_store)

    # 注册路由
    app.include_router(router)

    # 添加健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查接口"""
        if memory_store is None:
            return {
                "status": "unhealthy",
                "message": "记忆存储未初始化成功，请检查数据库连接和日志"
            }
        return {"status": "healthy", "message": "服务运行正常"}

    # 在模块加载时记录日志
    logging.info("应用初始化完成")

    return app


def create_health_app() -> FastAPI:
    """创建健康检查应用"""
    health_app = FastAPI(
        title="健康检查API",
        description="健康检查服务",
        version="1.0.0"
    )

    @health_app.get("/health")
    async def health_check():
        """健康检查接口"""
        return {"status": "healthy", "message": "服务运行正常"}

    return health_app
