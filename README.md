# Agent记忆向量存储系统

基于OceanBase的Agent记忆向量存储系统，提供对Agent记忆的增删改查功能，支持基于双向量搜索和多维度权重的智能检索。

## 功能特点

- **双向量存储**：同时存储内容向量(content embedding)和话题向量(topic embedding)，支持更精准的语义检索
- **智能权重系统**：支持基于重要程度、可信度、时效性、内容相似度、话题相似度等多个维度的加权检索
- **数据库层面优化**：在SQL层面直接应用向量权重，避免应用层重复计算，性能更优
- **完整的CRUD API**：提供完整的创建、读取、更新、删除API接口，支持单条和批量操作
- **灵活的过滤条件**：支持多种过滤条件组合查询，包括复杂条件操作符
- **高性能向量检索**：基于HNSW算法的高性能向量检索，支持L2距离计算
- **雪花ID生成**：使用雪花算法生成唯一ID，无需依赖Redis等外部服务
- **智能默认值**：合理的默认值设计，空值时默认为低重要性和低可信度

## 系统架构

系统由以下几个主要组件构成：

1. **FastAPI应用**：提供RESTful API接口
2. **AgentMemoryStore**：封装对OceanBase的操作，提供记忆存储和检索功能
3. **OpenAI嵌入模型**：将文本转换为向量表示
4. **OceanBase数据库**：存储记忆数据和向量
5. **雪花ID生成器**：生成唯一ID

## 安装与配置

### 环境要求

- Python 3.8+
- OceanBase 4.0+（支持向量存储功能）

### 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖：
- `pyobvector>=0.2.11`: OceanBase向量存储Python SDK（提供SQLAlchemy支持和向量搜索）
- `sqlalchemy>=2.0.0`: SQL操作库（用于CRUD操作和连接池）
- `pymysql>=1.1.0`: MySQL驱动程序（用于SQLAlchemy）
- `cryptography>=41.0.0`: 加密库（PyMySQL依赖）
- `fastapi>=0.104.1`: Web框架
- `uvicorn>=0.24.0`: ASGI服务器
- `pydantic>=2.5.0`: 数据验证库
- `python-multipart>=0.0.6`: 文件上传支持

## 架构说明

本项目采用混合架构设计，结合SQLAlchemy和ObVecClient的优势：

- **SQLAlchemy + OceanBase方言**: 用于CRUD操作、连接池管理和事务处理
- **ObVecClient**: 用于高性能的向量相似度搜索
- **pyobvector VECTOR类型**: 原生支持OceanBase的VECTOR字段，确保向量数据正确存储

这种设计解决了以下问题：
1. **连接池问题**: 使用SQLAlchemy的连接池管理，避免"MySQL server has gone away"错误
2. **向量搜索性能**: 保留ObVecClient的高性能向量搜索能力
3. **数据一致性**: 使用原生VECTOR类型，确保向量数据正确存储和搜索

### 配置

编辑`memory_server/config/settings.py`文件，设置以下配置：

```python
# 连接配置（包含连接池参数）
connection_args = {
    "host": "your_oceanbase_host",
    "port": "your_oceanbase_port",
    "user": "your_username",
    "password": "your_password",
    "db_name": "your_database",
    # SQLAlchemy连接池配置
    "pool_size": 10,           # 连接池大小
    "max_overflow": 20,        # 最大溢出连接数
    "pool_timeout": 30,        # 连接超时时间（秒）
    "pool_recycle": 3600,      # 连接回收时间（秒），1小时
    "pool_pre_ping": True,     # 连接前检查连接是否有效
    "echo": False              # 是否打印SQL语句
}

# OpenAI API配置
openai_config = {
    "model": "text-embedding-ada-002",  # embedding模型名
    "openai_api_key": "your_api_key",   # API密钥
    "openai_api_base": "your_api_base"  # embedding服务baseurl
}

# 服务配置
service_config = {
    "host": "0.0.0.0",
    "port": 8000,
    "health_port": 9195  # 健康检查端口
}
```

## 数据库表结构

OceanBase 中需要创建一个双向量表，用于存储Agent的记忆内容及其对应的内容向量和话题向量。以下是完整的建表语句：

```sql
CREATE TABLE agent_memory (
    id VARCHAR(32) PRIMARY KEY COMMENT '记忆ID，使用雪花算法生成',
    agent_id VARCHAR(64) NOT NULL DEFAULT 'memory_agent' COMMENT 'Agent ID',
    domain VARCHAR(100) NOT NULL DEFAULT '' COMMENT '所属领域',
    memory_type VARCHAR(50) NOT NULL DEFAULT '' COMMENT '记忆类型',
    content_type VARCHAR(50) NOT NULL DEFAULT '' COMMENT '内容类型',
    topic VARCHAR(255) DEFAULT NULL COMMENT '记忆话题',
    content TEXT NOT NULL COMMENT '记忆内容',
    embedding VECTOR(1024) NOT NULL COMMENT '内容的向量嵌入，1024维',
    topic_embedding VECTOR(1024) NOT NULL COMMENT '话题的向量嵌入，1024维',
    importance FLOAT NOT NULL DEFAULT 0.1 COMMENT '重要程度，默认为低重要性',
    confidence FLOAT NOT NULL DEFAULT 0.3 COMMENT '可信度，默认为低可信度',
    ttl VARCHAR(100) DEFAULT NULL COMMENT '过期时间（字符串格式）',
    related_user_id VARCHAR(64) DEFAULT NULL COMMENT '关联用户ID',
    meta_data TEXT DEFAULT NULL COMMENT '额外元数据（JSON字符串）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 双向量索引：使用HNSW算法
    VECTOR INDEX idx_memory_embedding_hnsw(embedding)
    WITH (distance=l2, type=hnsw, lib=vsag),

    VECTOR INDEX idx_memory_topic_embedding_hnsw(topic_embedding)
    WITH (distance=l2, type=hnsw, lib=vsag),

    -- 其他索引
    INDEX idx_agent_id (agent_id),
    INDEX idx_domain (domain),
    INDEX idx_ttl (ttl)
) COMMENT='Agent 双向量记忆表，支持内容和话题双向量搜索';
```

### 表结构说明

1. **双向量设计**：
   - `embedding`: 存储内容的向量嵌入，用于内容语义相似度搜索
   - `topic_embedding`: 存储话题的向量嵌入，用于话题相似度搜索

2. **智能默认值**：
   - `importance`: 默认为0.1（低重要性），避免未知信息获得不应有的高分
   - `confidence`: 默认为0.3（低可信度），保守的可信度评估

3. **性能优化**：
   - 两个向量列都使用HNSW算法索引，支持高效的L2距离计算
   - 常用查询字段都有相应的索引优化

## 启动服务

### 启动主服务

```bash
python run.py
```

服务将在`http://0.0.0.0:8000`上运行。

### 启动健康检查服务

```bash
python run.py health
```

健康检查服务将在`http://0.0.0.0:9195`上运行。

## API接口

### 1. 添加Agent记忆

**接口**：`POST /add_memories/`

**功能**：将结构化的Agent记忆添加到向量存储中，自动生成内容和话题的向量嵌入

**请求体**：
```json
[
  {
    "agentId": "agent_001",
    "domain": "医疗",
    "memoryType": "通用",
    "contentType": "陈述",
    "topic": "健康饮食",
    "content": "多吃蔬菜水果有助于身体健康。",
    "importance": 0.8,
    "confidence": 0.95,
    "ttl": null,
    "relatedUserId": "user_123",
    "metadata": {"source": "知识库"}
  }
]
```

**请求参数说明**：
- `agentId` (string, 可选): Agent的唯一标识，默认为"memory_agent"
- `domain` (string, 必需): 记忆所属领域，如医疗、金融等
- `memoryType` (string, 必需): 记忆类型，如通用或专属
- `contentType` (string, 必需): 内容类型，如陈述、事实、对话等
- `topic` (string, 可选): 记忆话题（主题分类），用于生成topic_embedding
- `content` (string, 必需): 具体记忆内容（文本），用于生成embedding
- `importance` (float, 可选): 重要程度，范围0~1，默认0.1（低重要性）
- `confidence` (float, 可选): 可信度评分，范围0~1，默认0.3（低可信度）
- `ttl` (datetime, 可选): 时效性（过期时间），ISO格式的日期时间字符串
- `relatedUserId` (string, 可选): 关联用户ID（例如用户发言人）
- `metadata` (object, 可选): 额外元数据（任意JSON对象）

**响应参数说明**：
```json
{
  "status": "success",           // 操作状态：success/error
  "message": "已写入1条agent记忆",  // 操作结果描述
  "ids": ["7047532952522752"],    // 生成的记忆ID列表
  "timing": {                     // 性能统计信息
    "data_preparation": "0.002s",
    "add_memories": "0.156s",
    "total": "0.158s"
  }
}
```

### 2. 查询记忆（双向量搜索和多维度重排序）

**接口**：`POST /query_similar/`

**功能**：根据查询文本，检索相似的记忆内容，使用双向量搜索和多维度重排序

**实现逻辑**：
1. 生成查询文本的向量嵌入
2. 在SQL层面执行双向量搜索，直接应用content和topic权重
3. 对结果进行多维度重排序（importance/confidence/recency）
4. 按综合得分排序返回结果

**请求体**：
```json
{
  "query": "健康饮食",
  "k": 4,
  "filters": {"domain": "医疗"},
  "importanceWeight": 0.3,
  "confidenceWeight": 0.2,
  "recencyWeight": 0.1,
  "contentWeights": 0.4,
  "topicWeights": 0.3
}
```

**请求参数说明**：
- `query` (string, 可选): 查询文本，用于生成向量嵌入进行相似度搜索
- `k` (int, 可选): 返回结果数量，默认4，必须大于0
- `filters` (object, 可选): 过滤条件，支持等值匹配和复杂操作符($ne, $in等)
- `importanceWeight` (float, 可选): 重要程度权重 (0-1)，默认0.2，在重排序阶段应用
- `confidenceWeight` (float, 可选): 可信度权重 (0-1)，默认0.2，在重排序阶段应用
- `recencyWeight` (float, 可选): 时效性权重 (0-1)，默认0.1，在重排序阶段应用
- `contentWeights` (float, 可选): 内容向量权重 (0-1)，默认1.0，在SQL层面直接应用到vector_distance结果
- `topicWeights` (float, 可选): 话题向量权重 (0-1)，默认1.0，在SQL层面直接应用到vector_distance结果

**响应参数说明**：
```json
{
  "status": "success",              // 操作状态：success/error
  "results": [                      // 查询结果列表，按score降序排列
    {
      "id": "7047532952522752",      // 记忆唯一ID
      "agent_id": "agent_001",       // Agent ID
      "domain": "医疗",             // 所属领域
      "memory_type": "通用",        // 记忆类型
      "content_type": "陈述",       // 内容类型
      "topic": "健康饮食",         // 记忆话题
      "content": "多吃蔬菜水果有助于身体健康。", // 记忆内容
      "importance": 0.8,             // 重要程度 (0-1)
      "confidence": 0.95,            // 可信度 (0-1)
      "ttl": null,                   // 过期时间
      "related_user_id": "user_123", // 关联用户ID
      "metadata": {"source": "知识库"}, // 元数据
      "created_at": "2023-06-01T10:15:30", // 创建时间
      "updated_at": "2023-06-01T10:15:30", // 更新时间
      "distance": 0.15,              // 加权后的向量距离（越小越相似）
      "score": 0.92                  // 综合得分（越高排名越前）
    }
  ],
  "timing": {                       // 性能统计信息
    "parameter_validation": "0.001s", // 参数验证耗时
    "query_execution": "0.045s",     // 查询执行耗时
    "total": "0.046s"                // 总耗时
  }
}
```

**注意事项**：
- 所有权重参数都必须在[0,1]范围内
- `contentWeights`和`topicWeights`在SQL层面应用，其他权重在重排序阶段应用
- 结果按综合得分(`score`)降序排列，得分越高表示越相关

### 3. 根据过滤条件删除记忆

**接口**：`POST /delete/`

**功能**：根据过滤条件删除符合的记忆数据

**请求体**：
```json
{
  "filterDict": {
    "domain": "过时领域",
    "importance": {"$lt": 0.3}
  }
}
```

**参数说明**：
- `filterDict`: 过滤条件字典，支持复杂条件

**响应**：
```json
{
  "status": "success",
  "message": "已删除5条符合条件的数据"
}
```

### 4. 根据ID更新记忆

**接口**：`POST /memories/update/{memory_id}`

**功能**：根据ID更新记忆数据

**请求体**：
```json
{
  "newData": {
    "importance": 0.9,
    "confidence": 0.98,
    "topic": "更新后的话题"
  }
}
```

**参数说明**：
- `memory_id`: 路径参数，记忆ID
- `newData`: 要更新的字段及其新值

**响应**：
```json
{
  "status": "success",
  "message": "成功更新ID为7047532952522752的记录"
}
```

### 5. 批量更新记忆

**接口**：`POST /memories/batch_update`

**功能**：批量根据ID更新记忆数据

**请求体**：
```json
{
  "items": [
    {
      "id": "7047532952522752",
      "newData": {
        "importance": 0.9,
        "confidence": 0.98
      }
    },
    {
      "id": "7047532952522753",
      "newData": {
        "topic": "新话题",
        "content": "更新后的内容"
      }
    }
  ]
}
```

**参数说明**：
- `items`: 更新项列表，每项包含id和newData

**响应**：
```json
{
  "status": "success",
  "message": "成功更新2条记录"
}
```

### 6. 根据ID删除记忆

**接口**：`POST /memories/delete/{memory_id}`

**功能**：根据ID删除记忆数据

**参数说明**：
- `memory_id`: 路径参数，记忆ID

**响应**：
```json
{
  "status": "success",
  "message": "成功删除ID为7047532952522752的记录"
}
```

### 7. 批量删除记忆

**接口**：`POST /memories/batch_delete`

**功能**：批量根据ID删除记忆数据

**请求体**：
```json
{
  "ids": ["7047532952522752", "7047532952522753", "7047532952522754"]
}
```

**参数说明**：
- `ids`: 要删除的记忆ID列表

**响应**：
```json
{
  "status": "success",
  "message": "成功删除3条记录"
}
```

### 9. 综合操作接口

**接口**：`POST /memories/operations`

**功能**：批量处理记忆的新建、更新和删除操作

**请求体**：
```json
{
  "operations": [
    {
      "operation": "create",
      "memories": [
        {
          "agentId": "agent_001",
          "domain": "医疗",
          "memoryType": "通用",
          "contentType": "陈述",
          "topic": "健康饮食",
          "content": "多吃蔬菜水果有助于身体健康",
          "importance": 0.8,
          "confidence": 0.95,
          "relatedUserId": "user_123",
          "metadata": {"source": "知识库"}
        },
        {
          "agentId": "agent_001",
          "domain": "医疗",
          "memoryType": "通用",
          "contentType": "陈述",
          "topic": "运动健身",
          "content": "每天适量运动可以增强免疫力",
          "importance": 0.7,
          "confidence": 0.9,
          "relatedUserId": "user_123",
          "metadata": {"source": "知识库"}
        }
      ]
    },
    {
      "operation": "update",
      "updates": [
        {
          "id": "7047532952522752",
          "newData": {
            "importance": 0.9,
            "confidence": 0.98
          }
        },
        {
          "id": "7047532952522753",
          "newData": {
            "topic": "更新后的话题",
            "content": "更新后的内容"
          }
        }
      ]
    },
    {
      "operation": "delete",
      "memory_ids": ["7047532952522754", "7047532952522755", "7047532952522756"]
    },
    {
      "operation": "delete",
      "filter_dict": {
        "domain": "过时领域",
        "importance": {"$lt": 0.3}
      }
    }
  ]
}
```

**参数说明**：
- `operations`: 操作列表，每个操作包含以下字段：
  - `operation`: 操作类型，可选值：create、update、delete
  - `memories`: 创建操作时的记忆列表
  - `updates`: 更新操作时的更新项列表
  - `memory_ids`: 删除操作时的记忆ID列表
  - `filter_dict`: 删除操作时的过滤条件

**响应**：
```json
{
  "status": "success",
  "operations": {
    "create": {
      "count": 2,
      "ids": ["7047532952522757", "7047532952522758"]
    },
    "update": {
      "count": 2,
      "ids": ["7047532952522752", "7047532952522753"]
    },
    "delete": {
      "count": 5,
      "ids": ["7047532952522754", "7047532952522755", "7047532952522756"]
    }
  },
  "errors": []
}
```

## 字段说明

| 字段名 | 类型 | 说明 | 默认值 |
|-------|------|------|--------|
| id | VARCHAR(32) | 主键，雪花ID字符串 | 自动生成 |
| agent_id | VARCHAR(64) | Agent的唯一标识 | "memory_agent" |
| domain | VARCHAR(100) | 记忆所属领域，如医疗、金融等 | "" |
| memory_type | VARCHAR(50) | 记忆类型，如通用或专属 | "" |
| content_type | VARCHAR(50) | 内容类型，如陈述、事实、对话等 | "" |
| topic | VARCHAR(255) | 记忆话题（主题分类），用于生成topic_embedding | NULL |
| content | TEXT | 具体记忆内容（文本），用于生成embedding | 必需 |
| embedding | VECTOR(1024) | 内容的向量嵌入表示，1024维 | 自动生成 |
| topic_embedding | VECTOR(1024) | 话题的向量嵌入表示，1024维 | 自动生成 |
| importance | FLOAT | 重要程度，范围0~1，空值时默认为低重要性 | 0.1 |
| confidence | FLOAT | 可信度评分，范围0~1，空值时默认为低可信度 | 0.3 |
| ttl | VARCHAR(100) | 时效性（过期时间），字符串格式 | NULL |
| related_user_id | VARCHAR(64) | 关联用户ID（例如用户发言人） | NULL |
| meta_data | TEXT | 额外元数据（JSON字符串序列化存储） | NULL |
| created_at | DATETIME | 创建时间 | CURRENT_TIMESTAMP |
| updated_at | DATETIME | 更新时间 | CURRENT_TIMESTAMP |

## 智能权重系统说明

系统采用分层权重架构，在数据库层面和应用层面分别应用不同的权重，实现高效的多维度智能检索。

### 分层权重架构

#### **第一层：SQL层面权重（数据库层面）**

在SQL查询中直接应用，计算加权向量距离：
```sql
distance = vector_distance(embedding, query_vector) * contentWeights +
           vector_distance(topic_embedding, query_vector) * topicWeights
```

1. **内容向量权重（contentWeights）**：
   - 控制内容语义相似度在最终距离中的权重
   - 范围0-1，默认1.0
   - 在SQL层面直接与vector_distance结果相乘

2. **话题向量权重（topicWeights）**：
   - 控制话题相似度在最终距离中的权重
   - 范围0-1，默认1.0
   - 在SQL层面直接与vector_distance结果相乘

#### **第二层：重排序层面权重（应用层面）**

在应用层面对SQL结果进行多维度重排序：

3. **重要程度权重（importanceWeight）**：
   - 控制记忆重要程度对结果排序的影响
   - 范围0-1，默认0.2
   - 值越大，重要程度高的记忆排名越靠前

4. **可信度权重（confidenceWeight）**：
   - 控制记忆可信度对结果排序的影响
   - 范围0-1，默认0.2
   - 值越大，可信度高的记忆排名越靠前

5. **时效性权重（recencyWeight）**：
   - 控制记忆时效性对结果排序的影响
   - 范围0-1，默认0.1
   - 值越大，越近期的记忆排名越靠前（365天内线性衰减）

### 综合得分计算公式

系统采用统一的“越高越好”得分标准：

```
最终得分 = similarity_score * remaining_weight +
           importance_score +
           confidence_score +
           recency_score

其中：
- similarity_score = 1 - (weighted_distance / max_distance)  // 基于加权距离转换
- remaining_weight = 1 - importanceWeight - confidenceWeight - recencyWeight
- importance_score = importance * importanceWeight
- confidence_score = confidence * confidenceWeight
- recency_score = (1 - days/365) * recencyWeight
```

### 性能优势

1. **数据库层面优化**：向量权重在SQL中直接计算，避免应用层重复计算
2. **统一排序标准**：所有得分都遵循“越高越好”原则，排序结果一致
3. **智能默认值**：空值时默认为低重要性和低可信度，避免不应有的高分
4. **灵活配置**：支持根据业务场景动态调整各维度权重

## 雪花ID生成器

系统使用雪花算法（Snowflake）生成唯一ID，无需依赖Redis等外部服务。雪花ID是一个64位的整数，由以下部分组成：

1. 1位符号位，始终为0
2. 41位时间戳（毫秒级）
3. 10位工作机器ID（5位数据中心ID + 5位机器ID）
4. 12位序列号（毫秒内的计数）

这种方式可以保证在分布式环境下生成的ID全局唯一，且按时间递增，便于排序和查询。

## 项目结构

```
.
├─ memory_server/         # 主包
│   ├─ __init__.py
│   ├─ __main__.py       # 主入口点
│   ├─ api/             # API相关
│   │   ├─ __init__.py
│   │   └─ router.py     # API路由定义
│   ├─ core/            # 核心功能
│   │   ├─ __init__.py
│   │   └─ app.py        # 应用初始化
│   ├─ db/              # 数据库相关
│   │   ├─ __init__.py
│   │   └─ memory_store.py # 记忆存储实现
│   ├─ models/          # 数据模型
│   │   ├─ __init__.py
│   │   └─ schemas.py    # 数据模型定义
│   ├─ utils/           # 工具函数
│   │   ├─ __init__.py
│   │   └─ snowflake.py  # 雪花ID生成器
│   └─ config/          # 配置文件
│       ├─ __init__.py
│       ├─ settings.py    # 配置设置
│       ├─ gunicorn_conf.py      # Gunicorn主服务配置
│       └─ gunicorn_health_conf.py # Gunicorn健康检查配置
├─ tests/              # 测试目录
├─ run.py              # 启动脚本
├─ requirements.txt    # 依赖列表
├─ Dockerfile          # Docker配置
├─ supervisord.conf    # Supervisor配置
└─ start.sh            # 启动脚本
```

## 许可证

MIT
