from pyobvector import ObVecClient, cosine_distance
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker
import json
import time
import math
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
from contextlib import contextmanager

# 导入数据库模型
from models.schemas import AgentMemoryModel
# 导入雪花ID生成器
from utils.snowflake import generate_id
# 导入告警工具
from utils.feishu_alarm import send_error_alarm


class AgentMemoryStore:
    def __init__(self, connection_args: Dict[str, Any], embeddings_model=None):
        """初始化Agent记忆存储

        Args:
            connection_args: 连接参数，包含host, port, user, password, db_name及连接池配置
            embeddings_model: 嵌入模型，需要有embed_query方法
        """
        # 保存连接参数
        self.connection_args = connection_args
        self.host = connection_args.get("host", "localhost")
        self.port = connection_args.get("port", "3306")
        self.user = connection_args.get("user", "root")
        self.password = connection_args.get("password", "")
        self.db_name = connection_args.get("db_name", "test")

        # 连接池配置
        self.pool_size = connection_args.get("pool_size", 10)
        self.max_overflow = connection_args.get("max_overflow", 20)
        self.pool_timeout = connection_args.get("pool_timeout", 30)
        self.pool_recycle = connection_args.get("pool_recycle", 3600)
        self.pool_pre_ping = connection_args.get("pool_pre_ping", True)
        self.echo = connection_args.get("echo", False)

        self.table_name = "agent_memory"
        self.embeddings_model = embeddings_model

        # 初始化日志
        self.logger = logging.getLogger("AgentMemoryStore")

        # 初始化ObVecClient（包含连接池和向量搜索功能）
        self._init_obvec_client()

    def normalize_vector(self, vector: List[float]) -> List[float]:
        """
        归一化向量到单位长度（用于余弦距离计算）

        Args:
            vector: 原始向量

        Returns:
            归一化后的向量
        """
        if not vector:
            return vector

        # 计算向量的模长
        magnitude = math.sqrt(sum(x * x for x in vector))

        # 避免除零错误
        if magnitude == 0:
            self.logger.warning("发现零向量，跳过归一化")
            return vector

        # 归一化
        normalized = [x / magnitude for x in vector]

        # 验证归一化结果（调试用）
        if self.logger.isEnabledFor(logging.DEBUG):
            new_magnitude = math.sqrt(sum(x * x for x in normalized))
            if abs(new_magnitude - 1.0) > 1e-6:
                self.logger.warning(f"归一化后模长不为1: {new_magnitude}")

        return normalized

    def _init_obvec_client(self):
        """初始化ObVecClient（包含连接池和向量搜索功能）"""
        try:
            # 使用ObVecClient作为统一的数据库客户端，包含连接池配置
            self.client = ObVecClient(
                uri=f"{self.host}:{self.port}",
                user=self.user,
                password=self.password,
                db_name=self.db_name,
                # 连接池配置
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
                pool_pre_ping=self.pool_pre_ping
            )

            # 获取ObVecClient内部的SQLAlchemy engine
            self.engine = self.client.engine

            # 创建会话工厂（使用ObVecClient的engine）
            self.session_local = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

            # 设置HNSW搜索参数
            self.client.set_ob_hnsw_ef_search(64)

            # 记录连接池信息
            pool = self.engine.pool
            self.logger.info(f"成功初始化ObVecClient: {self.host}:{self.port}, 数据库: {self.db_name}")
            self.logger.info(f"连接池配置: pool_size={pool.size()}, max_overflow={pool._max_overflow}, "
                             f"pool_timeout={pool._timeout}, pool_recycle={pool._recycle}, pool_pre_ping={pool._pre_ping}")

        except Exception as e:
            error_msg = f"初始化ObVecClient失败: {str(e)}"
            self.logger.error(error_msg)
            # 发送初始化失败告警
            send_error_alarm(error_msg, "ObVecClient初始化失败", str(e))
            raise

    @contextmanager
    def get_session(self):
        """获取数据库会话（上下文管理器）"""
        session = self.session_local()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def _generate_topic_embedding(self, topic: Optional[str]) -> List[float]:
        """生成topic的向量嵌入（未归一化）

        Args:
            topic: 话题内容，可以为空

        Returns:
            向量嵌入列表，如果topic为空则返回零向量（未归一化）
            注意：返回的向量需要在调用方进行归一化
        """
        if not topic or not topic.strip():
            # 如果topic为空，返回1024维的零向量
            return [0.0] * 1024

        if self.embeddings_model is None:
            raise ValueError("未设置嵌入模型，无法生成向量")

        # 生成topic的向量嵌入（未归一化）
        return self.embeddings_model.embed_query(topic.strip())

    def add_memory(self,
                   content: str,
                   agent_id: str = "memory_agent",
                   domain: str = "",
                   memory_type: str = "",
                   content_type: str = "",
                   topic: Optional[str] = None,
                   importance: float = 0.1,
                   confidence: float = 0.3,
                   ttl: Optional[datetime] = None,
                   related_user_id: Optional[str] = None,
                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """添加一条记忆

        Args:
            content: 记忆内容
            agent_id: Agent ID，默认为"memory_agent"
            domain: 所属领域
            memory_type: 记忆类型
            content_type: 内容类型
            topic: 记忆话题
            importance: 重要程度 (0-1)，默认为0.1（低重要性）
            confidence: 可信度 (0-1)，默认为0.3（低可信度）
            ttl: 过期时间
            related_user_id: 关联用户ID
            metadata: 额外元数据

        Returns:
            记忆ID字符串
        """
        if self.embeddings_model is None:
            raise ValueError("未设置嵌入模型，无法生成向量")

        # 生成内容的向量嵌入
        embedding_start = time.time()
        raw_embedding = self.embeddings_model.embed_query(content)
        # 归一化向量（余弦距离要求）
        embedding = self.normalize_vector(raw_embedding)
        embedding_time = time.time() - embedding_start
        self.logger.info(f"内容向量嵌入生成完成（已归一化），耗时: {embedding_time:.3f}秒")

        # 生成topic的向量嵌入
        topic_embedding_start = time.time()
        raw_topic_embedding = self._generate_topic_embedding(topic)
        # 归一化topic向量
        topic_embedding = self.normalize_vector(raw_topic_embedding)
        topic_embedding_time = time.time() - topic_embedding_start
        self.logger.info(f"topic向量嵌入生成完成（已归一化），耗时: {topic_embedding_time:.3f}秒")

        # 生成雪花ID
        memory_id = generate_id()

        try:
            # 数据库插入阶段
            db_insert_start = time.time()

            with self.get_session() as session:
                # 创建记忆对象，直接使用向量列表（pyobvector的VECTOR类型会自动处理）
                memory = AgentMemoryModel(
                    id=memory_id,
                    agent_id=agent_id,
                    domain=domain,
                    memory_type=memory_type,
                    content_type=content_type,
                    topic=topic,
                    content=content,
                    embedding=embedding,  # 直接使用向量列表
                    topic_embedding=topic_embedding,  # topic的向量嵌入
                    importance=importance,
                    confidence=confidence,
                    ttl=ttl.isoformat() if ttl else None,
                    related_user_id=related_user_id,
                    meta_data=json.dumps(metadata, ensure_ascii=False) if metadata else None
                )

                session.add(memory)
                session.flush()  # 确保数据被插入

            db_insert_time = time.time() - db_insert_start

            self.logger.info(f"成功添加记忆，ID: {memory_id}, "
                             f"内容向量嵌入耗时: {embedding_time:.3f}秒, "
                             f"topic向量嵌入耗时: {topic_embedding_time:.3f}秒, "
                             f"数据库插入耗时: {db_insert_time:.3f}秒")
            return memory_id
        except Exception as e:
            error_msg = f"添加记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "添加记忆失败", str(e))
            raise

    def add_memories(self, memories: List[Dict[str, Any]]) -> List[str]:
        """批量添加记忆

        Args:
            memories: 记忆列表，每个记忆是一个字典

        Returns:
            记忆ID列表
        """
        if self.embeddings_model is None:
            raise ValueError("未设置嵌入模型，无法生成向量")

        memory_ids = []
        data_list = []

        # 向量嵌入阶段
        embedding_start = time.time()
        total_embedding_time = 0
        total_topic_embedding_time = 0

        self.logger.info(f"开始批量生成向量嵌入，数量: {len(memories)}")

        for i, memory in enumerate(memories):
            # 获取内容
            content = memory.get("content")
            if not content:
                raise ValueError("记忆内容不能为空")

            # 生成单个内容的向量嵌入
            single_embedding_start = time.time()
            raw_embedding = self.embeddings_model.embed_query(content)
            # 归一化向量
            embedding = self.normalize_vector(raw_embedding)
            single_embedding_time = time.time() - single_embedding_start
            total_embedding_time += single_embedding_time

            # 生成topic的向量嵌入
            topic_embedding_start = time.time()
            raw_topic_embedding = self._generate_topic_embedding(memory.get("topic"))
            # 归一化topic向量
            topic_embedding = self.normalize_vector(raw_topic_embedding)
            topic_embedding_time = time.time() - topic_embedding_start
            total_topic_embedding_time += topic_embedding_time

            # 每处理100条记录打印一次进度
            if (i + 1) % 100 == 0 or (i + 1) == len(memories):
                avg_embedding_time = total_embedding_time / (i + 1)
                avg_topic_embedding_time = total_topic_embedding_time / (i + 1)
                self.logger.info(f"向量嵌入进度: {i + 1}/{len(memories)}, "
                                 f"平均内容嵌入耗时: {avg_embedding_time:.3f}秒/条, "
                                 f"平均topic嵌入耗时: {avg_topic_embedding_time:.3f}秒/条")

            # 生成雪花ID
            memory_id = generate_id()
            memory_ids.append(memory_id)

            # 准备数据，创建记忆对象（使用pyobvector的VECTOR类型）
            memory_obj = AgentMemoryModel(
                id=memory_id,
                agent_id=memory.get("agentId", "memory_agent"),
                domain=memory.get("domain", ""),
                memory_type=memory.get("memoryType", ""),
                content_type=memory.get("contentType", ""),
                topic=memory.get("topic"),
                content=content,
                embedding=embedding,  # 直接使用向量列表
                topic_embedding=topic_embedding,  # topic的向量嵌入
                importance=memory.get("importance", 0.1),
                confidence=memory.get("confidence", 0.3),
                ttl=memory.get("ttl").isoformat() if memory.get("ttl") else None,
                related_user_id=memory.get("relatedUserId"),
                meta_data=json.dumps(memory.get("metadata"), ensure_ascii=False) if memory.get("metadata") else None
            )
            data_list.append(memory_obj)

        # 向量嵌入总耗时
        total_embedding_phase_time = time.time() - embedding_start

        try:
            # 数据库批量插入阶段
            db_insert_start = time.time()

            with self.get_session() as session:
                # 批量添加所有记忆对象
                session.add_all(data_list)
                session.flush()  # 确保数据被插入

            db_insert_time = time.time() - db_insert_start

            self.logger.info(f"成功批量添加{len(data_list)}条记忆, "
                             f"向量嵌入总耗时: {total_embedding_phase_time:.3f}秒, "
                             f"平均内容嵌入耗时: {total_embedding_time / len(memories):.3f}秒/条, "
                             f"平均topic嵌入耗时: {total_topic_embedding_time / len(memories):.3f}秒/条, "
                             f"数据库插入耗时: {db_insert_time:.3f}秒")
            return memory_ids
        except Exception as e:
            error_msg = f"批量添加记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "批量添加记忆失败", str(e))
            raise

    def query(self,
              query: str,
              k: int = 4,
              filters: Optional[Dict[str, Any]] = None,
              importance_weight: float = 0.2,
              confidence_weight: float = 0.2,
              recency_weight: float = 0.1,
              content_weights: float = 1.0,
              topic_weights: float = 1.0) -> List[Dict[str, Any]]:
        """根据文本查询相似记忆，使用双向量搜索和多维度重排序

        实现逻辑：
        1. 生成查询文本的向量嵌入
        2. 在SQL层面执行双向量搜索，直接应用content和topic权重
        3. 对结果进行多维度重排序（importance/confidence/recency）
        4. 按综合得分排序返回结果

        Args:
            query: 查询文本
            k: 返回结果数量
            filters: 过滤条件，支持$ne、$in等操作符
            importance_weight: 重要程度权重 (0-1)，在重排序阶段应用
            confidence_weight: 可信度权重 (0-1)，在重排序阶段应用
            recency_weight: 时效性权重 (0-1)，在重排序阶段应用
            content_weights: 内容向量权重 (0-1)，在SQL层面直接应用到vector_distance结果
            topic_weights: 话题向量权重 (0-1)，在SQL层面直接应用到vector_distance结果

        Returns:
            按综合得分排序的相似记忆列表，每个记忆包含计算后的score字段
        """
        if self.embeddings_model is None:
            raise ValueError("未设置嵌入模型，无法生成向量")

        # 生成查询文本的向量嵌入
        embedding_start = time.time()
        raw_query_embedding = self.embeddings_model.embed_query(query)
        # 归一化查询向量（余弦距离要求）
        query_embedding = self.normalize_vector(raw_query_embedding)
        embedding_time = time.time() - embedding_start

        # 检查向量维度和模长
        if query_embedding:
            vector_norm = math.sqrt(sum(x*x for x in query_embedding))
            self.logger.info(f"查询文本: '{query}', 向量嵌入完成（已归一化），维度: {len(query_embedding)}, 模长: {vector_norm:.4f}, 耗时: {embedding_time:.3f}秒")

        # 步骤2：执行双向量搜索，在SQL层面直接应用content和topic权重
        memories = self._get_dual_vector_search_results(query_embedding, k, filters, content_weights, topic_weights)
        if not memories:
            return []

        # 步骤3：计算重排序得分（只处理importance/confidence/recency权重）
        self.logger.info(f"获取双向量搜索结果: {len(memories)}条，开始重排序")

        self._calculate_reranking_scores(
            memories, importance_weight, confidence_weight, recency_weight
        )

        # 步骤4：按综合得分排序并返回结果
        final_results = self._sort_and_limit_results(memories, k)
        # 输出权重配置信息
        self.logger.info(f"当前权重配置: contentWeights={content_weights}, topicWeights={topic_weights}, "
                         f"importanceWeight={importance_weight}, confidenceWeight={confidence_weight}, recencyWeight={recency_weight}")
        return final_results

    def query_similar_by_vector(self,
                                query_embedding: List[float],
                                k: int = 4,
                                filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """根据向量查询相似记忆

        Args:
            query_embedding: 查询向量
            k: 返回结果数量
            filters: 过滤条件

        Returns:
            相似记忆列表
        """
        if not self.client:
            error_msg = "ObVecClient未初始化，无法执行向量搜索"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "ObVecClient初始化失败", "client is None")
            return []  # 返回空结果

        try:
            where_clause = self._build_where_clause(filters)
            memories = self._execute_vector_search(query_embedding, k, where_clause)
            return memories
        except Exception as e:
            error_msg = f"查询相似记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "向量搜索失败", str(e))
            self.logger.error("向量搜索失败，返回空结果")
            return []  # 返回空结果

    def _build_where_clause(self, filters: Optional[Dict[str, Any]]) -> Optional[List]:
        """构建 WHERE 子句"""
        if not filters:
            return None

        try:
            where_conditions = []
            for key, value in filters.items():
                condition = self._build_single_condition(key, value)
                if condition:
                    where_conditions.append(condition)

            return [text(" AND ".join(where_conditions))] if where_conditions else None
        except Exception as e:
            self.logger.error(f"构建WHERE子句失败: {str(e)}")
            return None

    def _build_single_condition(self, key: str, value: Any) -> Optional[str]:
        """构建单个条件"""
        if isinstance(value, dict):
            return self._build_complex_condition(key, value)
        elif value is None:
            return f"{key} IS NULL"
        elif isinstance(value, (int, float)):
            return f"{key} = {value}"
        else:
            return f"{key} = '{value}'"

    def _build_complex_condition(self, key: str, value_dict: Dict[str, Any]) -> Optional[str]:
        """构建复杂条件"""
        conditions = []
        for op, op_value in value_dict.items():
            if op == "$gt":
                conditions.append(f"{key} > {op_value}")
            elif op == "$gte":
                conditions.append(f"{key} >= {op_value}")
            elif op == "$lt":
                conditions.append(f"{key} < {op_value}")
            elif op == "$lte":
                conditions.append(f"{key} <= {op_value}")
            elif op == "$ne":
                conditions.append(f"{key} != '{op_value}'")
            elif op == "$in":
                values = ", ".join([f"'{v}'" for v in op_value])
                conditions.append(f"{key} IN ({values})")
            else:
                self.logger.warning(f"不支持的操作符: {op}")

        return " AND ".join(conditions) if conditions else None

    def _execute_vector_search(self, query_embedding: List[float], k: int, where_clause: Optional[List]) -> List[
        Dict[str, Any]]:
        """执行向量搜索"""
        db_search_start = time.time()

        results = self.client.ann_search(
            table_name=self.table_name,
            vec_data=query_embedding,
            vec_column_name="embedding",
            distance_func=cosine_distance,
            topk=k,
            output_column_names=[
                "id", "agent_id", "domain", "memory_type", "content_type",
                "topic", "content", "importance", "confidence", "ttl",
                "related_user_id", "meta_data", "created_at", "updated_at"
            ],
            where_clause=where_clause,
            with_dist=True
        )

        db_search_time = time.time() - db_search_start
        memories = self._process_vector_search_results(results)

        self.logger.info(f"查询到{len(memories)}条相似记忆, 数据库搜索耗时: {db_search_time:.3f}秒")
        return memories

    def _process_vector_search_results(self, results) -> List[Dict[str, Any]]:
        """处理向量搜索结果"""
        result_process_start = time.time()
        memories = []

        # 安全转换数值字段
        def safe_float(value, default=0.0):
            if value is None or value == '':
                return default
            try:
                return float(value)
            except (ValueError, TypeError):
                return default

        # 安全转换可为空的数值字段
        def safe_float_nullable(value, default=None):
            if value is None or value == '':
                return default
            try:
                return float(value)
            except (ValueError, TypeError):
                return default

        for row in results.fetchall():
            # 获取原始距离值
            content_distance = safe_float_nullable(row[14]) if len(row) > 14 else None
            topic_distance = safe_float_nullable(row[15]) if len(row) > 15 else None

            memory = {
                "id": row[0],
                "agent_id": row[1],
                "domain": row[2],
                "memory_type": row[3],
                "content_type": row[4],
                "topic": row[5],
                "content": row[6],
                "importance": safe_float(row[7], 0.1),
                "confidence": safe_float(row[8], 0.3),
                "ttl": row[9],
                "related_user_id": row[10],
                "metadata": json.loads(row[11]) if row[11] else None,
                "created_at": row[12],
                "updated_at": row[13],
                "content_distance": content_distance,
                "topic_distance": topic_distance
            }
            memories.append(memory)

        result_process_time = time.time() - result_process_start
        self.logger.info(f"结果处理耗时: {result_process_time:.3f}秒")
        return memories

    def query_by_filter(self, filters: Dict[str, Any], limit: int = 100) -> List[Dict[str, Any]]:
        """根据过滤条件查询记忆（备用方法，不应被主要流程调用）

        注意：这个方法主要用作备用方案，当ObVecClient初始化失败或向量搜索失败时使用。
        正常情况下应优先使用query()方法进行双向量搜索。

        特点：
        - 不支持向量相似度搜索
        - 只支持基于属性的过滤查询
        - 按created_at降序排序
        - distance固定返回0.0

        Args:
            filters: 过滤条件，支持$gt、$lt、$in等操作符
            limit: 返回结果数量限制

        Returns:
            符合条件的记忆列表，按创建时间降序排列
        """
        try:
            with self.get_session() as session:
                query = self._build_filter_query(session, filters)
                results = query.order_by(AgentMemoryModel.created_at.desc()).limit(limit).all()
                memories = self._convert_memories_to_dict(results)

                self.logger.info(f"根据过滤条件查询到{len(memories)}条记忆")
                return memories

        except Exception as e:
            error_msg = f"根据过滤条件查询记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "过滤查询失败", str(e))
            return []

    def _build_filter_query(self, session, filters: Dict[str, Any]):
        """构建过滤查询"""
        query = session.query(AgentMemoryModel)

        for key, value in filters.items():
            query = self._apply_single_filter(query, key, value)

        return query

    def _apply_single_filter(self, query, key: str, value: Any):
        """应用单个过滤条件"""
        if key == 'content_like':
            return query.filter(AgentMemoryModel.content.like(value))
        elif isinstance(value, dict):
            return self._apply_complex_filter(query, key, value)
        elif value is None:
            column = getattr(AgentMemoryModel, key, None)
            return query.filter(column.is_(None)) if column is not None else query
        else:
            column = getattr(AgentMemoryModel, key, None)
            return query.filter(column == value) if column is not None else query

    def _apply_complex_filter(self, query, key: str, value_dict: Dict[str, Any]):
        """应用复杂过滤条件"""
        column = getattr(AgentMemoryModel, key, None)
        if column is None:
            return query

        for op, op_value in value_dict.items():
            if op == "$gt":
                query = query.filter(column > op_value)
            elif op == "$gte":
                query = query.filter(column >= op_value)
            elif op == "$lt":
                query = query.filter(column < op_value)
            elif op == "$lte":
                query = query.filter(column <= op_value)
            elif op == "$ne":
                query = query.filter(column != op_value)
            elif op == "$in":
                query = query.filter(column.in_(op_value))
            else:
                self.logger.warning(f"不支持的操作符: {op}")

        return query

    def _convert_memories_to_dict(self, memories) -> List[Dict[str, Any]]:
        """将记忆对象转换为字典列表"""
        result = []
        for memory in memories:
            # 安全转换数值字段
            def safe_float(value, default=0.0):
                if value is None or value == '':
                    return default
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default

            memory_dict = {
                "id": memory.id,
                "agent_id": memory.agent_id,
                "domain": memory.domain,
                "memory_type": memory.memory_type,
                "content_type": memory.content_type,
                "topic": memory.topic,
                "content": memory.content,
                "importance": safe_float(memory.importance, 0.1),
                "confidence": safe_float(memory.confidence, 0.3),
                "ttl": memory.ttl,
                "related_user_id": memory.related_user_id,
                "metadata": json.loads(memory.meta_data) if memory.meta_data else None,
                "created_at": memory.created_at,
                "updated_at": memory.updated_at,
                "distance": 0.0  # 简化实现，返回固定值
            }
            result.append(memory_dict)
        return result

    def _calculate_reranking_scores(self, memories: List[Dict[str, Any]],
                                    importance_weight: float, confidence_weight: float, recency_weight: float):
        """计算重排序得分，基于多个维度的权重计算

        注意：content和topic权重已在SQL层面应用，这里只处理其他维度的权重

        统一得分标准：所有得分都是“越高越好”，最终按score DESC排序

        得分计算公式（新版）：
        total_score = similarity_score + importance_score + confidence_score + recency_score

        其中：
        - similarity_score: [0,1] 基于加权向量距离转换，已包含content和topic权重
        - importance_score: [0,importance_weight] 重要程度越高得分越高
        - confidence_score: [0,confidence_weight] 可信度越高得分越高
        - recency_score: [0,recency_weight] 时间越近得分越高

        注意：移除了remaining_weight逻辑，相似度得分不再乘以比例

        Args:
            memories: 记忆列表，每个记忆包含distance字段（已经应用了content/topic权重）
            importance_weight: 重要程度权重 (0-1)
            confidence_weight: 可信度权重 (0-1)
            recency_weight: 时效性权重 (0-1)
        """
        reranking_start = time.time()
        now = datetime.now()

        # 距离统计（用于调试）
        if memories:
            distances = [m["distance"] for m in memories]
            min_distance = min(distances)
            max_distance = max(distances)
            avg_distance = sum(distances) / len(distances)
            self.logger.info(f"距离统计: min={min_distance:.4f}, max={max_distance:.4f}, avg={avg_distance:.4f}")

        for memory in memories:
            # 计算各维度得分（统一标准：越高越好）
            similarity_score = self._calculate_similarity_score(memory)  # [0,1] 基于已加权的余弦距离
            importance_score = self._calculate_importance_score(memory, importance_weight)  # [0,importance_weight]

            # confidence直接乘以权重，确保在正确范围内
            confidence_value = memory["confidence"]
            confidence_value = max(0.0, min(1.0, confidence_value))  # 确保在[0,1]范围
            confidence_score = confidence_value * confidence_weight  # [0,confidence_weight]

            recency_score = self._calculate_recency_score(memory, now, recency_weight)  # [0,recency_weight]

            # 计算最终综合得分（移除remaining_weight逻辑）
            # 相似度得分不再乘以比例，直接与其他维度得分相加
            total_score = (
                    similarity_score +  # 相似度得分（已包含content和topic权重）
                    importance_score +  # 重要程度得分
                    confidence_score +  # 可信度得分
                    recency_score  # 时效性得分
            )

            memory["score"] = total_score

            # 添加各个分值的详细信息，便于调试
            memory["similarity_score"] = similarity_score
            memory["importance_score"] = importance_score
            memory["confidence_score"] = confidence_score
            memory["recency_score"] = recency_score

        reranking_time = time.time() - reranking_start
        self.logger.info(f"重排序得分计算耗时: {reranking_time:.3f}秒")

    def _calculate_similarity_score(self, memory: Dict[str, Any]) -> float:
        """计算相似度得分，基于已加权的余弦距离

        统一得分标准：所有得分都是“越高越好”
        - distance: 余弦距离越小越相似
        - similarity_score: 得分越高越相似

        使用余弦相似度公式，适用于归一化向量

        Args:
            memory: 记忆对象，包含distance字段（已经应用了content和topic权重）

        Returns:
            相似度得分 (0-1)，得分越高表示越相似
        """
        distance = memory["distance"]

        # 针对当前大距离值问题，使用更激进的相似度计算
        import math

        # 使用固定范围映射，确保相似度得分在合理范围内
        # 这样可以避免业务权重主导排序的问题

        # 使用余弦距离的相似度计算（针对归一化向量优化）
        # 归一化向量的余弦距离范围: [0, 2]
        # - distance=0: 完全相同（向量完全一致）
        # - distance=1: 正交（向量无关）
        # - distance=2: 完全相反（向量方向相反）

        # 直接使用余弦相似度公式: similarity = 1 - distance/2
        # 这样可以得到更精确的相似度得分
        similarity_score = 1.0 - (distance / 2.0)

        # 确保得分在[0,1]范围内
        similarity_score = max(0.0, min(1.0, similarity_score))

        # 现在的映射关系：
        # - distance=0.0 -> similarity=1.0 (完全相同)
        # - distance=0.2 -> similarity=0.9 (非常相似)
        # - distance=0.5 -> similarity=0.75 (高相似)
        # - distance=1.0 -> similarity=0.5 (中等相似)
        # - distance=1.5 -> similarity=0.25 (低相似)
        # - distance=2.0 -> similarity=0.0 (完全不相似)

        # 确保得分在[0,1]范围内
        return max(0.0, min(1.0, similarity_score))

    def _calculate_importance_score(self, memory: Dict[str, Any], importance_weight: float) -> float:
        """计算重要程度得分

        统一得分标准：重要程度越高，得分越高

        Args:
            memory: 记忆对象
            importance_weight: 重要程度权重 (0-1)

        Returns:
            加权后的重要程度得分 (0-importance_weight)，得分越高越重要
        """
        importance_value = memory["importance"]
        return importance_value * importance_weight

    def _calculate_recency_score(self, memory: Dict[str, Any], now: datetime, recency_weight: float) -> float:
        """计算时效性得分，越近期的记忆得分越高

        统一得分标准：时间越近，得分越高
        - 今天创建的记忆：得分 = recency_weight
        - 365天前创建的记忆：得分 = 0
        - 线性衰减

        Args:
            memory: 记忆对象
            now: 当前时间
            recency_weight: 时效性权重 (0-1)

        Returns:
            加权后的时效性得分 (0-recency_weight)，得分越高表示越近期
        """
        if recency_weight <= 0:
            return 0.0

        created_at = memory["created_at"]
        if not created_at:
            return 0.0

        # 计算时间差异（天数）
        days_diff = (now - created_at).days

        # 计算时效性得分：365天内线性衰减，超过365天得分为0
        if days_diff >= 365:
            return 0.0

        # 线性衰减：1 - (days_diff / 365)
        recency_factor = 1.0 - (days_diff / 365.0)
        recency_factor = max(0.0, min(1.0, recency_factor))

        return recency_factor * recency_weight

    def _sort_and_limit_results(self, memories: List[Dict[str, Any]], k: int) -> List[Dict[str, Any]]:
        """按综合得分排序并限制结果数量

        统一排序标准：按score DESC排序（得分越高排名越前）
        这与所有得分计算的“越高越好”标准一致

        Args:
            memories: 包含score字段的记忆列表
            k: 返回结果数量

        Returns:
            按得分降序排列的前k条记忆
        """
        sort_start = time.time()

        # 按score降序排序（得分越高排名越前）
        memories.sort(key=lambda x: x["score"], reverse=True)
        sort_time = time.time() - sort_start

        final_results = memories[:k]

        self.logger.info(f"排序耗时: {sort_time:.3f}秒, 返回{len(final_results)}条结果")
        return final_results

    def _get_dual_vector_search_results(self, query_embedding: List[float], k: int, filters: Optional[Dict[str, Any]],
                                        content_weights: float = 1.0, topic_weights: float = 1.0) -> List[
        Dict[str, Any]]:
        """获取双向量搜索结果，在代码中计算加权距离

        实现逻辑：
        1. 同时搜索content和topic两个向量列
        2. SQL中返回原始content_distance和topic_distance
        3. 在代码中计算加权后的综合距离：content_distance * content_weight + topic_distance * topic_weight
        4. 数据库按加权距离排序，但不重复计算vector_distance

        Args:
            query_embedding: 查询向量
            k: 返回结果数量
            filters: 过滤条件
            content_weights: 内容向量权重，在代码中应用
            topic_weights: 话题向量权重，在代码中应用

        Returns:
            按加权距离排序的记忆列表，包含content_distance、topic_distance和distance字段
        """
        dual_search_start = time.time()

        if not self.client:
            error_msg = "ObVecClient未初始化，无法执行向量搜索"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "ObVecClient初始化失败", "client is None")
            return []  # 返回空结果，不使用备用方法

        try:
            # 直接使用数据库层面的双向量搜索，传入权重参数
            all_memories = self._execute_database_level_dual_vector_search(query_embedding, k, filters, content_weights,
                                                                           topic_weights)

            dual_search_time = time.time() - dual_search_start
            self.logger.info(f"双向量综合搜索完成，获得{len(all_memories)}条结果, "
                             f"耗时: {dual_search_time:.3f}秒")

            return all_memories

        except Exception as e:
            error_msg = f"双向量搜索失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "双向量搜索失败", str(e))
            self.logger.error("双向量搜索失败，返回空结果")
            return []  # 返回空结果，不使用备用方法

    def _execute_database_level_dual_vector_search(self, query_embedding: List[float], k: int,
                                                   filters: Optional[Dict[str, Any]], content_weights: float = 1.0,
                                                   topic_weights: float = 1.0) -> List[Dict[str, Any]]:
        """在数据库层面执行双向量搜索，在代码中计算加权距离

        核心SQL逻辑：
        SELECT *,
               vector_distance(embedding, :vector1) as content_distance,
               vector_distance(topic_embedding, :vector2) as topic_distance
        FROM table
        ORDER BY (content_distance * :content_weight + topic_distance * :topic_weight)

        优势：
        1. 避免在SQL中重复计算vector_distance函数
        2. 数据库按加权距离排序，但只计算一次vector_distance
        3. 在代码中灵活计算最终的加权距离
        4. 返回原始距离值供进一步分析

        Args:
            query_embedding: 查询向量
            k: 返回结果数量
            filters: 过滤条件
            content_weights: 内容向量权重
            topic_weights: 话题向量权重

        Returns:
            按加权距离排序的记忆列表
        """
        search_start = time.time()

        # 构建过滤条件
        where_conditions = []
        params = {}

        if filters:
            param_counter = 0
            for key, value in filters.items():
                if isinstance(value, dict):
                    if "$ne" in value:
                        param_name = f"param_{param_counter}"
                        where_conditions.append(f"{key} != :{param_name}")
                        params[param_name] = value["$ne"]
                        param_counter += 1
                    elif "$in" in value:
                        param_name = f"param_{param_counter}"
                        where_conditions.append(f"{key} IN :{param_name}")
                        params[param_name] = tuple(value["$in"])
                        param_counter += 1
                else:
                    param_name = f"param_{param_counter}"
                    where_conditions.append(f"{key} = :{param_name}")
                    params[param_name] = value
                    param_counter += 1

        # 添加向量非空条件
        where_conditions.append("(embedding IS NOT NULL OR topic_embedding IS NOT NULL)")
        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 将向量转换为字符串格式
        vector_str = str(query_embedding)
        params['vector1'] = vector_str
        params['vector2'] = vector_str
        params['content_weight'] = content_weights
        params['topic_weight'] = topic_weights
        params['limit_k'] = k

        # 构建双向量搜索SQL查询
        sql_query = self._build_dual_vector_search_sql(where_clause)

        try:
            # 使用SQLAlchemy engine直接执行查询
            with self.engine.connect() as connection:
                results = connection.execute(text(sql_query), params)

                # 使用现有的结果处理方法来保持一致性
                memories = self._process_vector_search_results(results)

                # 在代码中计算加权距离
                for memory in memories:
                    content_distance = memory.get('content_distance')
                    topic_distance = memory.get('topic_distance')

                    # 计算加权后的综合距离
                    weighted_distance = 0.0
                    if content_distance is not None:
                        weighted_distance += content_distance * content_weights
                    if topic_distance is not None:
                        weighted_distance += topic_distance * topic_weights

                    memory['distance'] = weighted_distance

        except Exception as e:
            self.logger.error(f"数据库层面双向量搜索失败: {str(e)}")
            # 如果失败，返回空结果
            return []

        search_time = time.time() - search_start
        self.logger.info(f"数据库层面双向量搜索耗时: {search_time:.3f}秒, 获得{len(memories)}条结果, "
                         f"content权重: {content_weights}, topic权重: {topic_weights}")

        return memories

    def _build_dual_vector_search_sql(self, where_clause: str) -> str:
        """构建双向量搜索的SQL查询语句

        将复杂的SQL查询提取到单独的方法中，提高代码可读性和维护性

        SQL逻辑说明：
        1. 选择所有基本字段，处理importance和confidence的空值情况
        2. 计算原始向量距离：content_distance和topic_distance
        3. 按加权距离排序，但不重复计算vector_distance
        4. 返回原始距离值供代码层面计算加权综合距离

        Args:
            where_clause: WHERE子句字符串，包含过滤条件和向量非空条件

        Returns:
            完整的SQL查询语句，使用命名参数绑定
        """
        return f"""
        SELECT
            id, agent_id, domain, memory_type, content_type,
            topic, content,
            CASE
                WHEN importance = '' OR importance IS NULL THEN 0.1
                ELSE CAST(importance AS DECIMAL(10,2))
            END as importance,
            CASE
                WHEN confidence = '' OR confidence IS NULL THEN 0.3
                ELSE CAST(confidence AS DECIMAL(10,2))
            END as confidence,
            ttl, related_user_id, meta_data, created_at, updated_at,
            COALESCE(
                CASE WHEN embedding IS NOT NULL
                     THEN vector_distance(embedding, :vector1, cosine)
                     ELSE NULL
                END,
                NULL
            ) as content_distance,
            COALESCE(
                CASE WHEN topic_embedding IS NOT NULL
                     THEN vector_distance(topic_embedding, :vector2, cosine)
                     ELSE NULL
                END,
                NULL
            ) as topic_distance
        FROM {self.table_name}
        {where_clause}
        ORDER BY (
            COALESCE(
                CASE WHEN embedding IS NOT NULL
                     THEN vector_distance(embedding, :vector1, cosine) * :content_weight
                     ELSE 0
                END,
                0
            ) +
            COALESCE(
                CASE WHEN topic_embedding IS NOT NULL
                     THEN vector_distance(topic_embedding, :vector2, cosine) * :topic_weight
                     ELSE 0
                END,
                0
            )
        ) ASC
        LIMIT :limit_k
        """

    def update_memory(self, memory_id: str, new_data: Dict[str, Any]) -> bool:
        """更新记忆

        Args:
            memory_id: 记忆ID
            new_data: 新数据

        Returns:
            是否成功更新
        """
        try:
            with self.get_session() as session:
                # 查找要更新的记忆
                memory = session.query(AgentMemoryModel).filter(AgentMemoryModel.id == memory_id).first()
                if not memory:
                    self.logger.warning(f"未找到ID为{memory_id}的记忆")
                    return False

                # 更新字段
                for key, value in new_data.items():
                    # 转换驼峰命名为下划线命名
                    db_key = ''.join(['_' + c.lower() if c.isupper() else c for c in key]).lstrip('_')

                    if db_key == "content" and self.embeddings_model:
                        # 如果更新内容，需要重新生成向量嵌入
                        setattr(memory, db_key, value)
                        embedding = self.embeddings_model.embed_query(value)
                        setattr(memory, "embedding", embedding)  # 使用pyobvector的VECTOR类型
                    elif db_key == "topic" and self.embeddings_model:
                        # 如果更新topic，需要重新生成topic向量嵌入
                        setattr(memory, db_key, value)
                        topic_embedding = self._generate_topic_embedding(value)
                        setattr(memory, "topic_embedding", topic_embedding)
                    elif db_key == "metadata" and value:
                        # 处理metadata字段，映射到meta_data字段
                        if isinstance(value, dict):
                            setattr(memory, "meta_data", json.dumps(value, ensure_ascii=False))
                        else:
                            setattr(memory, "meta_data", value)
                    elif hasattr(memory, db_key):
                        setattr(memory, db_key, value)

                session.flush()  # 确保更新被提交

            self.logger.info(f"成功更新记忆: {memory_id}")
            return True
        except Exception as e:
            error_msg = f"更新记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "更新记忆失败", str(e))
            raise

    def batch_update_memories(self, items: List[Dict[str, Any]]) -> int:
        """批量更新记忆

        Args:
            items: 更新项列表，每项包含id和newData

        Returns:
            成功更新的记录数
        """
        try:
            with self.get_session() as session:
                updated_count = self._process_batch_updates(session, items)
                session.flush()  # 确保所有更新被提交

            self.logger.info(f"成功批量更新{updated_count}条记忆")
            return updated_count
        except Exception as e:
            error_msg = f"批量更新记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "批量更新记忆失败", str(e))
            raise

    def _process_batch_updates(self, session, items: List[Dict[str, Any]]) -> int:
        """处理批量更新"""
        updated_count = 0

        for item in items:
            memory_id = item.get("id")
            new_data = item.get("newData")

            if not memory_id or not new_data:
                continue

            memory = session.query(AgentMemoryModel).filter(AgentMemoryModel.id == memory_id).first()
            if not memory:
                self.logger.warning(f"未找到ID为{memory_id}的记忆")
                continue

            self._update_memory_fields(memory, new_data)
            updated_count += 1

        return updated_count

    def _update_memory_fields(self, memory, new_data: Dict[str, Any]):
        """更新记忆字段"""
        for key, value in new_data.items():
            db_key = self._convert_to_snake_case(key)

            if db_key == "content" and self.embeddings_model:
                self._update_content_with_embedding(memory, value)
            elif db_key == "topic" and self.embeddings_model:
                self._update_topic_with_embedding(memory, value)
            elif db_key == "metadata" and value:
                self._update_metadata_field(memory, value)
            elif hasattr(memory, db_key):
                setattr(memory, db_key, value)

    def _convert_to_snake_case(self, key: str) -> str:
        """转换驼峰命名为下划线命名"""
        return ''.join(['_' + c.lower() if c.isupper() else c for c in key]).lstrip('_')

    def _update_content_with_embedding(self, memory, content: str):
        """更新内容并重新生成嵌入"""
        setattr(memory, "content", content)
        raw_embedding = self.embeddings_model.embed_query(content)
        # 归一化向量
        embedding = self.normalize_vector(raw_embedding)
        setattr(memory, "embedding", embedding)

    def _update_topic_with_embedding(self, memory, topic: str):
        """更新topic并重新生成嵌入"""
        setattr(memory, "topic", topic)
        raw_topic_embedding = self._generate_topic_embedding(topic)
        # 归一化topic向量
        topic_embedding = self.normalize_vector(raw_topic_embedding)
        setattr(memory, "topic_embedding", topic_embedding)

    def _update_metadata_field(self, memory, metadata_value):
        """更新元数据字段"""
        if isinstance(metadata_value, dict):
            setattr(memory, "meta_data", json.dumps(metadata_value, ensure_ascii=False))
        else:
            setattr(memory, "meta_data", metadata_value)

    def delete_memory(self, memory_id: str) -> bool:
        """删除记忆

        Args:
            memory_id: 记忆ID

        Returns:
            是否成功删除
        """
        try:
            with self.get_session() as session:
                # 查找要删除的记忆
                memory = session.query(AgentMemoryModel).filter(AgentMemoryModel.id == memory_id).first()
                if not memory:
                    self.logger.warning(f"未找到ID为{memory_id}的记忆")
                    return False

                # 删除记忆
                session.delete(memory)
                session.flush()  # 确保删除被提交

            self.logger.info(f"成功删除记忆: {memory_id}")
            return True
        except Exception as e:
            error_msg = f"删除记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "删除记忆失败", str(e))
            return False

    def batch_delete_memories(self, memory_ids: List[str]) -> int:
        """批量删除记忆

        Args:
            memory_ids: 记忆ID列表

        Returns:
            成功删除的记录数
        """
        if not memory_ids:
            return 0

        try:
            deleted_count = 0

            with self.get_session() as session:
                # 批量删除记忆
                deleted_count = session.query(AgentMemoryModel).filter(
                    AgentMemoryModel.id.in_(memory_ids)
                ).delete(synchronize_session=False)

                session.flush()  # 确保删除被提交

            self.logger.info(f"成功批量删除{deleted_count}条记忆")
            return deleted_count
        except Exception as e:
            error_msg = f"批量删除记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "批量删除记忆失败", str(e))
            return 0

    def delete_memories_by_filter(self, filters: Dict[str, Any]) -> int:
        """根据过滤条件删除记忆

        Args:
            filters: 过滤条件

        Returns:
            删除的记录数
        """
        try:
            deleted_count = 0

            with self.get_session() as session:
                # 构建查询
                query = session.query(AgentMemoryModel)

                # 应用过滤条件
                for key, value in filters.items():
                    if isinstance(value, dict):
                        # 处理复杂条件，如 {"importance": {"$gt": 0.3}}
                        for op, op_value in value.items():
                            column = getattr(AgentMemoryModel, key, None)
                            if column is None:
                                continue
                            if op == "$gt":
                                query = query.filter(column > op_value)
                            elif op == "$gte":
                                query = query.filter(column >= op_value)
                            elif op == "$lt":
                                query = query.filter(column < op_value)
                            elif op == "$lte":
                                query = query.filter(column <= op_value)
                            elif op == "$ne":
                                query = query.filter(column != op_value)
                            elif op == "$in":
                                query = query.filter(column.in_(op_value))
                            else:
                                self.logger.warning(f"不支持的操作符: {op}")
                    elif value is None:
                        column = getattr(AgentMemoryModel, key, None)
                        if column is not None:
                            query = query.filter(column.is_(None))
                    else:
                        column = getattr(AgentMemoryModel, key, None)
                        if column is not None:
                            query = query.filter(column == value)

                # 执行删除
                deleted_count = query.delete(synchronize_session=False)
                session.flush()  # 确保删除被提交

            if deleted_count > 0:
                self.logger.info(f"根据过滤条件成功删除{deleted_count}条记录")
            else:
                self.logger.info("没有找到符合条件的记录")

            return deleted_count

        except Exception as e:
            error_msg = f"根据过滤条件删除记忆失败: {str(e)}"
            self.logger.error(error_msg)
            send_error_alarm(error_msg, "按条件删除记忆失败", str(e))
            return 0
