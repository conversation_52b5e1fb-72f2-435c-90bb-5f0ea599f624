#!/usr/bin/env python3
"""
测试新增的content_distance和topic_distance字段功能
"""

import requests
import json
import sys

def test_query_with_distance_fields():
    """测试查询接口是否返回新的距离字段"""
    
    # 测试查询请求
    query_url = "http://localhost:8000/query/"
    query_data = {
        "query": "健康饮食",
        "k": 2,
        "contentWeights": 0.6,
        "topicWeights": 0.4,
        "importanceWeight": 0.2,
        "confidenceWeight": 0.2,
        "recencyWeight": 0.1
    }
    
    print("发送查询请求...")
    print(f"请求数据: {json.dumps(query_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(query_url, json=query_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n查询成功! 状态码: {response.status_code}")
            print(f"响应状态: {result.get('status')}")
            
            results = result.get('results', [])
            print(f"返回结果数量: {len(results)}")
            
            if results:
                print("\n检查第一个结果的距离字段:")
                first_result = results[0]
                
                # 检查必要字段
                required_fields = ['id', 'content', 'distance']
                new_fields = ['content_distance', 'topic_distance']
                
                print("基本字段检查:")
                for field in required_fields:
                    value = first_result.get(field)
                    print(f"  {field}: {value}")
                
                print("\n新增距离字段检查:")
                for field in new_fields:
                    value = first_result.get(field)
                    if value is not None:
                        print(f"  ✓ {field}: {value}")
                    else:
                        print(f"  ✗ {field}: 缺失或为None")
                
                # 显示完整的第一个结果
                print(f"\n完整的第一个结果:")
                print(json.dumps(first_result, ensure_ascii=False, indent=2))
                
                # 验证逻辑
                distance = first_result.get('distance')
                content_distance = first_result.get('content_distance')
                topic_distance = first_result.get('topic_distance')
                
                print(f"\n距离字段验证:")
                print(f"  综合距离 (distance): {distance}")
                print(f"  内容距离 (content_distance): {content_distance}")
                print(f"  话题距离 (topic_distance): {topic_distance}")
                
                if content_distance is not None and topic_distance is not None:
                    # 计算预期的综合距离（加权）
                    expected_distance = content_distance * 0.6 + topic_distance * 0.4
                    print(f"  预期综合距离 (0.6*content + 0.4*topic): {expected_distance:.6f}")
                    
                    if abs(distance - expected_distance) < 0.001:
                        print("  ✓ 距离计算验证通过")
                    else:
                        print("  ✗ 距离计算验证失败")
                
            else:
                print("没有返回结果，可能数据库中没有数据")
                
        else:
            print(f"查询失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("连接失败! 请确保服务器正在运行在 http://localhost:8000")
        return False
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("测试新增的content_distance和topic_distance字段")
    print("=" * 60)
    
    success = test_query_with_distance_fields()
    
    if success:
        print("\n测试完成!")
    else:
        print("\n测试失败!")
        sys.exit(1)
